import React from 'react'
import CustomContainer from '@/components/container'
import { alpha, Grid, Typography, useMediaQuery, Box } from '@mui/material'
import CustomImageContainer from '@/components/CustomImageContainer'
import { useTheme } from '@mui/styles'
import DollarSignHighlighter from '@/components/DollarSignHighlighter'
import CustomNextImage from '@/components/CustomNextImage'
import { useRouter } from 'next/router'
import { useDispatch } from 'react-redux'
import { setZoneData } from '@/redux/slices/global'

const AvailableZoneSection = ({ landingPageData }) => {
    const theme = useTheme()
    const router = useRouter()
    const dispatch = useDispatch()
    const isSmall = useMediaQuery(theme.breakpoints.down('sm'))
    const isMd = useMediaQuery(theme.breakpoints.down('md'))
    let languageDirection = undefined
    if (typeof window !== 'undefined') {
        languageDirection = localStorage.getItem('direction')
    }

    // Zone to restaurant mapping - maps zone IDs to their featured restaurant
    const zoneToRestaurantMapping = {
        1: { slug: 'riviera', zoneId: 7 }, // Roma -> Riviera restaurant
        2: { slug: 'darkitchen-pordenone', zoneId: 9 }, // Pordenone
        3: { slug: 'darkitchen-udine', zoneId: 10 }, // Udine
        4: { slug: 'belluno', zoneId: 11 }, // Belluno
        5: { slug: 'darkitchen-conegliano', zoneId: 12 }, // Conegliano
        6: { slug: 'darkitchen-montebelluna', zoneId: 13 }, // Montebelluna
        7: { slug: 'riviera', zoneId: 7 }, // Roma (if zone ID is 7) -> Riviera restaurant
        8: { slug: 'darkitchen-rimini', zoneId: 27 }, // Rimini
        9: { slug: 'darkitchen-san-dona', zoneId: 14 }, // San Donà
        10: { slug: 'darkitchen-portogruaro', zoneId: 15 }, // Portogruaro
        11: { slug: 'darkitchen-vicenza', zoneId: 16 }, // Vicenza
        12: { slug: 'darkitchen-padova', zoneId: 17 }, // Padova
        13: { slug: 'darkitchen-bolzano', zoneId: 18 }, // Bolzano
        14: { slug: 'darkitchen-cuneo', zoneId: 19 }, // Cuneo
        15: { slug: 'darkitchen-forli', zoneId: 20 }, // Forlì
        16: { slug: 'darkitchen-livorno', zoneId: 21 }, // Livorno
        17: { slug: 'darkitchen-latina', zoneId: 22 }, // Latina
        18: { slug: 'darkitchen-lucca', zoneId: 23 }, // Lucca
        19: { slug: 'darkitchen-firenze', zoneId: 24 }, // Firenze
        20: { slug: 'cosenza5', zoneId: 25 }, // Cosenza
        21: { slug: 'darkitchen-aquila', zoneId: 26 }, // L'Aquila
    }

    const handleZoneClick = (zone) => {
        // Debug: Log the zone data to console
        console.log('Zone clicked:', zone)
        console.log('Zone ID:', zone.id)
        console.log('Zone display_name:', zone.display_name)

        // Store the zone ID in localStorage
        localStorage.setItem('zoneid', zone.id)

        // Set zone data in Redux store
        dispatch(setZoneData(zone))

        // Check if there's a specific restaurant mapping for this zone
        const restaurantMapping = zoneToRestaurantMapping[zone.id]

        if (restaurantMapping) {
            // Special handling for external redirects to darkitchen.it
            // Using zone display names for more reliable matching
            const externalRedirectsByName = {
                'Roma': 'https://darkitchen.it/restaurant/riviera?restaurant_zone_id=7',
                'Pordenone': 'https://darkitchen.it/restaurant/darkitchen-pordenone?restaurant_zone_id=9',
                'Udine': 'https://darkitchen.it/restaurant/darkitchen-udine?restaurant_zone_id=10',
                'Belluno': 'https://darkitchen.it/restaurant/belluno?restaurant_zone_id=11',
                'Conegliano': 'https://darkitchen.it/restaurant/darkitchen-conegliano?restaurant_zone_id=12',
                'Montebelluna': 'https://darkitchen.it/restaurant/darkitchen-montebelluna?restaurant_zone_id=13',
                'San Donà di Piave': 'https://darkitchen.it/restaurant/darkitchen-san-dona?restaurant_zone_id=14',
                'San Donà': 'https://darkitchen.it/restaurant/darkitchen-san-dona?restaurant_zone_id=14',
                'Rimini': 'https://darkitchen.it/restaurant/darkitchen-rimini?restaurant_zone_id=27',
                'Portogruaro': 'https://darkitchen.it/restaurant/darkitchen-portogruaro?restaurant_zone_id=15',
                'Vicenza': 'https://darkitchen.it/restaurant/darkitchen-vicenza?restaurant_zone_id=16',
                'Padova': 'https://darkitchen.it/restaurant/darkitchen-padova?restaurant_zone_id=17',
                'Bolzano': 'https://darkitchen.it/restaurant/darkitchen-bolzano?restaurant_zone_id=18',
                'Cuneo': 'https://darkitchen.it/restaurant/darkitchen-cuneo?restaurant_zone_id=19',
                'Forlì': 'https://darkitchen.it/restaurant/darkitchen-forli?restaurant_zone_id=20',
                'Forli': 'https://darkitchen.it/restaurant/darkitchen-forli?restaurant_zone_id=20',
                'Livorno': 'https://darkitchen.it/restaurant/darkitchen-livorno?restaurant_zone_id=21',
                'Latina': 'https://darkitchen.it/restaurant/darkitchen-latina?restaurant_zone_id=22',
                'Lucca': 'https://darkitchen.it/restaurant/darkitchen-lucca?restaurant_zone_id=23',
                'Firenze': 'https://darkitchen.it/restaurant/darkitchen-firenze?restaurant_zone_id=24',
                'Cosenza': 'https://darkitchen.it/restaurant/cosenza5?restaurant_zone_id=25',
                'L\'Aquila': 'https://darkitchen.it/restaurant/darkitchen-aquila?restaurant_zone_id=26',
                'Aquila': 'https://darkitchen.it/restaurant/darkitchen-aquila?restaurant_zone_id=26',
            }

            // Check if this zone should redirect externally (by display name)
            const externalUrl = externalRedirectsByName[zone.display_name]
            if (externalUrl) {
                console.log('Redirecting to external URL:', externalUrl)
                window.location.href = externalUrl
                return
            }

            // Navigate to the specific restaurant for this zone (internal routing)
            router.push({
                pathname: `/restaurant/[id]`,
                query: {
                    id: restaurantMapping.slug,
                    restaurant_zone_id: restaurantMapping.zoneId,
                },
            })
        } else {
            // Fallback to home page if no specific restaurant mapping
            router.push('/home')
        }
    }
    return (
        <CustomContainer>
            <Grid
                sx={{
                    paddingTop: { xs: '50px', md: '60px' },
                    paddingBottom: { xs: '10px', md: '60px' },
                }}
                container
                alignItems="center"
                justifyContent="center"
                spacing={{ xs: 3, md: 4 }}
            >
                <Grid
                    item
                    xs={12}
                    sm={12}
                    md={6}
                    align={
                        isSmall
                            ? 'center'
                            : languageDirection === 'rtl'
                            ? 'right'
                            : 'left'
                    }
                >
                    <CustomNextImage src={landingPageData?.available_zone_image_full_url} width={isMd ? 300 : 440} height={isMd ? 250 : 380} objectFit="contain" />
                </Grid>
                <Grid
                    item
                    xs={12}
                    sm={12}
                    md={6}
                    align={isSmall ? 'center' : 'left'}
                >
                    <Typography
                        fontSize={{ xs: '1.2rem', md: '30px' }}
                        fontWeight={{ xs: '600', md: '700' }}
                        color={theme.palette.neutral[1000]}
                        marginBottom={{ xs: '8px', md: '12px' }}
                        component="h2"
                    >
                        <DollarSignHighlighter
                            text={landingPageData?.available_zone_title}
                        />
                    </Typography>
                    <Typography
                        fontSize={{ xs: '14px', md: '16px' }}
                        fontWeight={{ xs: '400', md: '400' }}
                        color={theme.palette.neutral[400]}
                        paddingTop={isSmall ? '10px' : '0rem'}
                        component="p"
                    >
                        {landingPageData?.available_zone_short_description}
                    </Typography>
                    <Box sx={{ position: 'relative', marginTop: '35px' }}>
                        {/* Scrollable container with custom scrollbar */}
                        <Box
                            sx={{
                                maxHeight: 200,
                                overflowY: 'auto',
                                paddingRight: '10px',
                                '&::-webkit-scrollbar': {
                                    width: '3px',
                                },
                                '&::-webkit-scrollbar-track': {
                                    backgroundColor: '#f0f0f0',
                                },
                                '&::-webkit-scrollbar-thumb': {
                                    backgroundColor: '#c1c1c1',
                                    borderRadius: '3px',
                                },
                                '&::-webkit-scrollbar-thumb:hover': {
                                    backgroundColor: '#003638',
                                },
                            }}
                        >
                            <Box
                                sx={{
                                    display: 'flex',
                                    flexWrap: 'wrap',
                                    gap: '12px',
                                    maxWidth: '543px',
                                    paddingBottom: '35px',
                                }}
                            >
                                {landingPageData?.available_zone_list?.map(
                                    (zone) => (
                                        <Box
                                            key={zone?.id}
                                            onClick={() => handleZoneClick(zone)}
                                            sx={{
                                                borderRadius: '10px',
                                                border: '1px solid',
                                                borderColor: alpha(
                                                    theme.palette.neutral[400],
                                                    0.2
                                                ),
                                                backgroundColor: (theme) =>
                                                    theme.palette.neutral[100],
                                                padding: '8px 30px',
                                                fontSize: '18px',
                                                fontWeight: 500,
                                                textAlign: 'center',
                                                textDecoration: 'none',
                                                color: (theme) =>
                                                    theme.palette.neutral[1000],
                                                cursor: 'pointer',
                                                transition: 'all 0.2s ease-in-out',
                                                '&:hover': {
                                                    boxShadow: `0px 4px 12px 0px ${alpha(
                                                        theme.palette
                                                            .neutral[1000],
                                                        0.1
                                                    )}`,
                                                    color: (theme) =>
                                                        theme.palette.primary
                                                            .main,
                                                    transform: 'translateY(-2px)',
                                                },
                                            }}
                                            component="div"
                                        >
                                            {zone?.display_name}
                                        </Box>
                                    )
                                )}
                            </Box>
                        </Box>

                        {/* The gradient overlay at the bottom */}
                        <Box
                            sx={{
                                position: 'absolute',
                                height: '62px',
                                bottom: 0,
                                left: 0,
                                width: '100%',
                                background: `linear-gradient(180deg, ${alpha(
                                    theme.palette.neutral[1800],
                                    0.0
                                )} 43.03%,  ${alpha(
                                    theme.palette.neutral[1800],
                                    0.72
                                )} 55.48%,  ${alpha(
                                    theme.palette.neutral[1800],
                                    0.9
                                )} 100%)`,
                                pointerEvents: 'none',
                            }}
                        />
                    </Box>
                </Grid>
            </Grid>
        </CustomContainer>
    )
}

export default AvailableZoneSection
